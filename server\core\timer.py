#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间同步模块
功能：基于苏宁时间接口获取准确的服务器时间，用于精确的秒杀时间控制
"""

import time
import requests
import json
import logging
from datetime import datetime
from typing import Optional, Tuple

logger = logging.getLogger(__name__)


class Timer:
    """时间同步器"""
    
    def __init__(self, sleep_interval: float = 0.1):
        """
        初始化时间同步器
        
        Args:
            sleep_interval: 时间检测间隔（秒）
        """
        self.sleep_interval = sleep_interval
        self.time_diff = 0  # 本地时间与服务器时间的差值（毫秒）
        self.last_sync_time = 0  # 上次同步时间
        self.sync_interval = 300  # 同步间隔（秒），5分钟同步一次
        
        # 初始化时间差
        self.sync_time()
        
    def get_suning_time(self) -> Optional[int]:
        """
        从苏宁服务器获取时间（毫秒）
        
        Returns:
            服务器时间戳（毫秒），失败返回None
        """
        try:
            url = "http://quan.suning.com/getSysTime.do"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                # 解析时间格式：20250720185146 -> 毫秒时间戳
                time_str = data.get('sysTime1', '')
                if len(time_str) == 14:  # YYYYMMDDHHMMSS
                    dt = datetime.strptime(time_str, '%Y%m%d%H%M%S')
                    return int(dt.timestamp() * 1000)
                else:
                    logger.warning(f"苏宁时间格式异常: {time_str}")
                    return None
            else:
                logger.warning(f"苏宁时间接口响应异常: {response.status_code}")
                return None
                
        except requests.RequestException as e:
            logger.warning(f"获取苏宁时间失败: {e}")
            return None
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.warning(f"解析苏宁时间失败: {e}")
            return None
            
    def get_jd_time(self) -> Optional[int]:
        """
        从京东服务器获取时间（毫秒）- 备用方案
        
        Returns:
            服务器时间戳（毫秒），失败返回None
        """
        try:
            url = "https://a.jd.com/ajax/queryServerData.html"
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                return int(data.get("serverTime", 0))
            else:
                logger.warning(f"京东时间接口响应异常: {response.status_code}")
                return None
                
        except requests.RequestException as e:
            logger.warning(f"获取京东时间失败: {e}")
            return None
        except (json.JSONDecodeError, ValueError, KeyError) as e:
            logger.warning(f"解析京东时间失败: {e}")
            return None
            
    def get_local_time(self) -> int:
        """
        获取本地时间（毫秒）
        
        Returns:
            本地时间戳（毫秒）
        """
        return int(round(time.time() * 1000))
        
    def sync_time(self) -> bool:
        """
        同步时间差
        
        Returns:
            同步是否成功
        """
        try:
            local_time = self.get_local_time()
            
            # 优先使用苏宁时间
            server_time = self.get_suning_time()
            time_source = "苏宁"
            
            # 如果苏宁时间获取失败，使用京东时间作为备用
            if server_time is None:
                server_time = self.get_jd_time()
                time_source = "京东"
                
            if server_time is not None:
                self.time_diff = local_time - server_time
                self.last_sync_time = local_time
                
                logger.info(f'时间同步成功，使用{time_source}时间服务器，'
                           f'本地时间与服务器时间差为【{self.time_diff}】毫秒')
                return True
            else:
                logger.error("所有时间服务器都无法访问，使用本地时间")
                self.time_diff = 0
                return False
                
        except Exception as e:
            logger.error(f"时间同步失败: {e}")
            self.time_diff = 0
            return False
            
    def get_server_time(self) -> int:
        """
        获取校正后的服务器时间（毫秒）
        
        Returns:
            校正后的服务器时间戳（毫秒）
        """
        # 检查是否需要重新同步
        current_time = self.get_local_time()
        if current_time - self.last_sync_time > self.sync_interval * 1000:
            self.sync_time()
            
        # 返回校正后的时间
        return self.get_local_time() - self.time_diff
        
    def wait_until(self, target_time: datetime, callback=None) -> None:
        """
        等待到指定时间
        
        Args:
            target_time: 目标时间
            callback: 可选的回调函数，用于在等待过程中执行
        """
        target_ms = int(target_time.timestamp() * 1000)
        
        logger.info(f'正在等待到达设定时间: {target_time.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]}，'
                   f'本地时间与服务器时间误差为【{self.time_diff}】毫秒')
        
        while True:
            current_server_time = self.get_server_time()
            
            if current_server_time >= target_ms:
                logger.info('时间到达，开始执行……')
                break
            else:
                remaining_ms = target_ms - current_server_time
                remaining_seconds = remaining_ms / 1000.0
                
                # 如果剩余时间大于1秒，显示倒计时
                if remaining_seconds > 1:
                    logger.info(f'距离目标时间还有 {remaining_seconds:.1f} 秒')
                    
                # 执行回调函数
                if callback:
                    try:
                        callback(remaining_seconds)
                    except Exception as e:
                        logger.warning(f"回调函数执行失败: {e}")
                        
                time.sleep(self.sleep_interval)
                
    def get_time_info(self) -> dict:
        """
        获取时间信息
        
        Returns:
            包含时间信息的字典
        """
        local_time = self.get_local_time()
        server_time = self.get_server_time()
        
        return {
            'local_time': local_time,
            'server_time': server_time,
            'time_diff': self.time_diff,
            'local_time_str': datetime.fromtimestamp(local_time / 1000).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
            'server_time_str': datetime.fromtimestamp(server_time / 1000).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
            'last_sync_time': self.last_sync_time,
            'sync_age_seconds': (local_time - self.last_sync_time) / 1000
        }


# 全局时间同步器实例
global_timer = Timer()


def get_accurate_time() -> int:
    """
    获取准确的服务器时间（毫秒）
    
    Returns:
        校正后的服务器时间戳（毫秒）
    """
    return global_timer.get_server_time()


def wait_until_time(target_time: datetime, callback=None) -> None:
    """
    等待到指定时间
    
    Args:
        target_time: 目标时间
        callback: 可选的回调函数
    """
    global_timer.wait_until(target_time, callback)


def get_time_status() -> dict:
    """
    获取时间同步状态
    
    Returns:
        时间状态信息
    """
    return global_timer.get_time_info()