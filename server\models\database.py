#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型定义
功能：定义MongoDB数据库结构和操作
"""

from pymongo import MongoClient
from datetime import datetime, timedelta
import uuid
from typing import Dict, List, Optional


class Database:
    """数据库管理器"""
    
    def __init__(self, connection_string="mongodb://localhost:27017/", db_name="taobao_seckill"):
        self.client = MongoClient(connection_string)
        self.db = self.client[db_name]
        
        # 集合引用
        self.users = self.db.users
        self.cookies = self.db.cookies
        self.tasks = self.db.tasks
        self.products = self.db.products
        self.logs = self.db.logs
        
        # 创建索引
        self._create_indexes()
        
    def _create_indexes(self):
        """创建数据库索引"""
        # 用户集合索引
        self.users.create_index("user_id", unique=True)
        self.users.create_index("username", unique=True)
        
        # Cookie集合索引
        self.cookies.create_index("user_id")
        self.cookies.create_index("created_at")
        self.cookies.create_index("expires_at")
        
        # 任务集合索引
        self.tasks.create_index("task_id", unique=True)
        self.tasks.create_index("user_id")
        self.tasks.create_index("status")
        self.tasks.create_index("scheduled_time")
        
        # 商品集合索引
        self.products.create_index("product_id", unique=True)
        self.products.create_index("product_url")
        
        # 日志集合索引
        self.logs.create_index("timestamp")
        self.logs.create_index("level")
        self.logs.create_index("task_id")


class UserModel:
    """用户模型"""
    
    def __init__(self, db: Database):
        self.db = db
        self.collection = db.users
        
    def create_user(self, username: str, nickname: str = None) -> str:
        """创建用户"""
        user_id = str(uuid.uuid4())
        user_data = {
            "user_id": user_id,
            "username": username,
            "nickname": nickname or username,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "status": "active",
            "login_count": 0,
            "last_login": None
        }
        
        self.collection.insert_one(user_data)
        return user_id
        
    def get_user(self, user_id: str) -> Optional[Dict]:
        """获取用户信息"""
        return self.collection.find_one({"user_id": user_id})
        
    def get_user_by_username(self, username: str) -> Optional[Dict]:
        """根据用户名获取用户"""
        return self.collection.find_one({"username": username})
        
    def update_user(self, user_id: str, update_data: Dict) -> bool:
        """更新用户信息"""
        update_data["updated_at"] = datetime.now()
        result = self.collection.update_one(
            {"user_id": user_id},
            {"$set": update_data}
        )
        return result.modified_count > 0
        
    def update_login_info(self, user_id: str) -> bool:
        """更新登录信息"""
        result = self.collection.update_one(
            {"user_id": user_id},
            {
                "$set": {"last_login": datetime.now()},
                "$inc": {"login_count": 1}
            }
        )
        return result.modified_count > 0


class CookieModel:
    """Cookie模型"""
    
    def __init__(self, db: Database):
        self.db = db
        self.collection = db.cookies
        
    def save_cookies(self, user_id: str, cookies: Dict, expires_days: int = 7) -> str:
        """保存Cookie"""
        cookie_id = str(uuid.uuid4())
        cookie_data = {
            "cookie_id": cookie_id,
            "user_id": user_id,
            "cookies": cookies,
            "created_at": datetime.now(),
            "expires_at": datetime.now() + timedelta(days=expires_days),
            "is_valid": True,
            "last_used": None,
            "use_count": 0
        }
        
        # 先将该用户的其他Cookie设为无效
        self.collection.update_many(
            {"user_id": user_id},
            {"$set": {"is_valid": False}}
        )
        
        # 插入新Cookie
        self.collection.insert_one(cookie_data)
        return cookie_id
        
    def get_valid_cookies(self, user_id: str) -> Optional[Dict]:
        """获取有效的Cookie"""
        cookie_data = self.collection.find_one({
            "user_id": user_id,
            "is_valid": True,
            "expires_at": {"$gt": datetime.now()}
        })
        
        if cookie_data:
            # 更新使用信息
            self.collection.update_one(
                {"cookie_id": cookie_data["cookie_id"]},
                {
                    "$set": {"last_used": datetime.now()},
                    "$inc": {"use_count": 1}
                }
            )
            
        return cookie_data
        
    def invalidate_cookies(self, user_id: str) -> bool:
        """使Cookie失效"""
        result = self.collection.update_many(
            {"user_id": user_id},
            {"$set": {"is_valid": False}}
        )
        return result.modified_count > 0
        
    def cleanup_expired_cookies(self) -> int:
        """清理过期Cookie"""
        result = self.collection.delete_many({
            "expires_at": {"$lt": datetime.now()}
        })
        return result.deleted_count


class TaskModel:
    """任务模型"""
    
    def __init__(self, db: Database):
        self.db = db
        self.collection = db.tasks
        
    def create_task(self, user_id: str, product_url: str, scheduled_time: datetime, 
                   task_type: str = "single", quantity: int = 1) -> str:
        """创建秒杀任务"""
        task_id = str(uuid.uuid4())
        task_data = {
            "task_id": task_id,
            "user_id": user_id,
            "product_url": product_url,
            "scheduled_time": scheduled_time,
            "task_type": task_type,  # single, batch
            "quantity": quantity,
            "status": "pending",  # pending, running, success, failed, cancelled
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "started_at": None,
            "completed_at": None,
            "result": None,
            "error_message": None,
            "retry_count": 0,
            "max_retries": 3
        }
        
        self.collection.insert_one(task_data)
        return task_id
        
    def get_task(self, task_id: str) -> Optional[Dict]:
        """获取任务信息"""
        return self.collection.find_one({"task_id": task_id})
        
    def get_user_tasks(self, user_id: str, status: str = None) -> List[Dict]:
        """获取用户任务列表"""
        query = {"user_id": user_id}
        if status:
            query["status"] = status
            
        return list(self.collection.find(query).sort("created_at", -1))
        
    def get_pending_tasks(self, limit: int = 100) -> List[Dict]:
        """获取待执行任务"""
        return list(self.collection.find({
            "status": "pending"
        }).limit(limit))
        
    def update_task_status(self, task_id: str, status: str, 
                          result: Dict = None, error_message: str = None) -> bool:
        """更新任务状态"""
        update_data = {
            "status": status,
            "updated_at": datetime.now()
        }
        
        if status == "running":
            update_data["started_at"] = datetime.now()
        elif status in ["success", "failed", "cancelled"]:
            update_data["completed_at"] = datetime.now()
            
        if result:
            update_data["result"] = result
            
        if error_message:
            update_data["error_message"] = error_message
            
        result = self.collection.update_one(
            {"task_id": task_id},
            {"$set": update_data}
        )
        return result.modified_count > 0
        
    def increment_retry_count(self, task_id: str) -> bool:
        """增加重试次数"""
        result = self.collection.update_one(
            {"task_id": task_id},
            {"$inc": {"retry_count": 1}}
        )
        return result.modified_count > 0


class ProductModel:
    """商品模型"""
    
    def __init__(self, db: Database):
        self.db = db
        self.collection = db.products
        
    def save_product(self, product_url: str, product_info: Dict) -> str:
        """保存商品信息"""
        product_id = str(uuid.uuid4())
        product_data = {
            "product_id": product_id,
            "product_url": product_url,
            "title": product_info.get("title", ""),
            "price": product_info.get("price", 0),
            "seller_id": product_info.get("seller_id", ""),
            "seller_name": product_info.get("seller_name", ""),
            "stock": product_info.get("stock", 0),
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "last_checked": datetime.now()
        }
        
        # 检查是否已存在
        existing = self.collection.find_one({"product_url": product_url})
        if existing:
            # 更新现有记录
            self.collection.update_one(
                {"product_url": product_url},
                {"$set": {**product_info, "updated_at": datetime.now()}}
            )
            return existing["product_id"]
        else:
            # 插入新记录
            self.collection.insert_one(product_data)
            return product_id
            
    def get_product(self, product_url: str) -> Optional[Dict]:
        """获取商品信息"""
        return self.collection.find_one({"product_url": product_url})


class LogModel:
    """日志模型"""
    
    def __init__(self, db: Database):
        self.db = db
        self.collection = db.logs
        
    def add_log(self, level: str, message: str, task_id: str = None, 
               user_id: str = None, extra_data: Dict = None):
        """添加日志"""
        log_data = {
            "timestamp": datetime.now(),
            "level": level,  # DEBUG, INFO, WARNING, ERROR, CRITICAL
            "message": message,
            "task_id": task_id,
            "user_id": user_id,
            "extra_data": extra_data or {}
        }
        
        self.collection.insert_one(log_data)
        
    def get_logs(self, level: str = None, task_id: str = None, 
                user_id: str = None, limit: int = 100) -> List[Dict]:
        """获取日志"""
        query = {}
        if level:
            query["level"] = level
        if task_id:
            query["task_id"] = task_id
        if user_id:
            query["user_id"] = user_id
            
        return list(self.collection.find(query).sort("timestamp", -1).limit(limit))
        
    def cleanup_old_logs(self, days: int = 30) -> int:
        """清理旧日志"""
        cutoff_date = datetime.now() - timedelta(days=days)
        result = self.collection.delete_many({
            "timestamp": {"$lt": cutoff_date}
        })
        return result.deleted_count
