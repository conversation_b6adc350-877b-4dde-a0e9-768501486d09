import requests
import parsel
import re
import json
import time
import threading
from datetime import datetime
from urllib.parse import quote, unquote
import random
from fake_useragent import UserAgent


class SpikeEngine:
    """秒杀引擎类"""
    
    def __init__(self, database_manager):
        self.db = database_manager
        self.ua = UserAgent()
        self.running_tasks = {}
        self.lock = threading.Lock()
    
    def parse_product_info(self, url, cookies, user_agent):
        """解析商品信息"""
        try:
            headers = {
                'User-Agent': user_agent,
                'Referer': 'https://www.taobao.com/',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            
            session = requests.Session()
            
            # 设置cookies - 修复cookie设置方式，避免重复设置
            try:
                if isinstance(cookies, str):
                    # 如果cookies是字符串，需要解析
                    import json
                    try:
                        cookies = json.loads(cookies)
                    except:
                        # 如果不是JSON格式，尝试按分号分割
                        cookie_dict = {}
                        for item in cookies.split(';'):
                            if '=' in item:
                                key, value = item.strip().split('=', 1)
                                cookie_dict[key] = value
                        cookies = cookie_dict
                
                # 清除现有cookies，避免重复
                session.cookies.clear()
                
                # 根据URL确定域名，避免重复设置
                if 'tmall.com' in url:
                    domain = '.tmall.com'
                else:
                    domain = '.taobao.com'
                
                for name, value in cookies.items():
                    session.cookies.set(name, value, domain=domain)
            except Exception as e:
                print(f"设置cookies失败: {str(e)}")
                raise Exception(f"设置cookies失败: {str(e)}")
            
            try:
                response = session.get(url, headers=headers, timeout=10)
                response.raise_for_status()
            except Exception as e:
                print(f"获取商品页面失败: {str(e)}")
                raise Exception(f"获取商品页面失败: {str(e)}")
            
            # 解析页面 - 参考原始版本的解析方式
            try:
                selector = parsel.Selector(response.text)
                page_source = response.text
            except Exception as e:
                print(f"解析页面失败: {str(e)}")
                raise Exception(f"解析页面失败: {str(e)}")
            
            # 提取商品信息 - 使用原始版本的方法
            product_info = {}
            
            # 提取seller_id
            try:
                seller_id = selector.xpath("//form[@id='J_FrmBid']/input[@name='seller_id']/@value").get()
                if not seller_id:
                    seller_match = re.search(r'"sellerId":"(\d+)"', page_source)
                    if seller_match:
                        seller_id = seller_match.group(1)
                product_info['seller_id'] = seller_id or ''
            except Exception as e:
                print(f"提取seller_id失败: {str(e)}")
                product_info['seller_id'] = ''
            
            # 提取photo_url
            try:
                photo_url = selector.xpath("//form[@id='J_FrmBid']/input[@name='photo_url']/@value").get()
                product_info['photo_url'] = photo_url or ''
            except Exception as e:
                print(f"提取photo_url失败: {str(e)}")
                product_info['photo_url'] = ''
            
            # 提取rootCatId
            try:
                rootCatId = selector.xpath("//form[@id='J_FrmBid']/input[@name='rootCatId']/@value").get()
                product_info['rootCatId'] = rootCatId or ''
            except Exception as e:
                print(f"提取rootCatId失败: {str(e)}")
                product_info['rootCatId'] = ''
            
            # 提取库存
            try:
                allow_quantity_match = re.findall(r'"quantity":(\d+),', page_source)
                if allow_quantity_match and len(allow_quantity_match) > 0:
                    product_info['allow_quantity'] = allow_quantity_match[0]
                else:
                    # 尝试其他库存模式
                    quantity_match2 = re.search(r'"quantity":(\d+)', page_source)
                    if quantity_match2:
                        product_info['allow_quantity'] = quantity_match2.group(1)
                    else:
                        product_info['allow_quantity'] = '1'
            except Exception as e:
                print(f"提取库存失败: {str(e)}")
                product_info['allow_quantity'] = '1'
            
            # 提取商品ID和SKU ID
            try:
                param_match = re.findall(r'id=(\d+).*?skuId=(\d+)', url)
                if param_match and len(param_match) > 0 and len(param_match[0]) >= 2:
                    product_info['item_id'] = param_match[0][0]
                    product_info['sku_id'] = param_match[0][1]
                    product_info['buy_param'] = f"{param_match[0][0]}_1_{param_match[0][1]}"
                else:
                    # 尝试分别提取
                    item_id_match = re.search(r'id=(\d+)', url)
                    sku_id_match = re.search(r'skuId=(\d+)', url)
                    
                    if item_id_match and sku_id_match:
                        product_info['item_id'] = item_id_match.group(1)
                        product_info['sku_id'] = sku_id_match.group(1)
                        product_info['buy_param'] = f"{product_info['item_id']}_1_{product_info['sku_id']}"
                    else:
                        raise Exception(f"无法从URL中提取商品ID和SKU ID: {url}")
            except Exception as e:
                print(f"提取商品ID和SKU ID失败: {str(e)}")
                raise Exception(f"提取商品ID和SKU ID失败: {str(e)}")
            
            # 提取价格
            try:
                price_match = re.findall(r'"price":"(\d+\.\d+)",', page_source)
                if price_match and len(price_match) > 0:
                    product_info['buy_now'] = price_match[0]
                else:
                    # 尝试其他价格模式
                    price_match2 = re.search(r'"price":"([^"]+)"', page_source)
                    if price_match2:
                        product_info['buy_now'] = price_match2.group(1)
                    else:
                        product_info['buy_now'] = '0.00'
                product_info['current_price'] = product_info['buy_now']
            except Exception as e:
                print(f"提取价格失败: {str(e)}")
                product_info['buy_now'] = '0.00'
                product_info['current_price'] = '0.00'
            
            # 提取seller_num_id
            try:
                seller_num_id = selector.xpath("//*[@id=\"dsr-userid\"]/@value").get()
                product_info['seller_num_id'] = seller_num_id or ''
            except Exception as e:
                print(f"提取seller_num_id失败: {str(e)}")
                product_info['seller_num_id'] = ''
            
            # 提取_tb_token_
            try:
                product_info['_tb_token_'] = cookies.get('_tb_token_', '')
            except Exception as e:
                print(f"提取_tb_token_失败: {str(e)}")
                product_info['_tb_token_'] = ''
            
            # 提取商品标题
            try:
                title = selector.xpath('//h1[@data-spm="1000983"]//text()').get()
                if not title:
                    title = selector.xpath('//title/text()').get()
                product_info['title'] = title.strip() if title else '商品详情'
            except Exception as e:
                print(f"提取商品标题失败: {str(e)}")
                product_info['title'] = '商品详情'
            
            print(f"商品信息解析成功: {product_info}")
            return product_info, page_source, session
            
        except Exception as e:
            print(f"解析商品信息失败: {str(e)}")
            raise Exception(f"解析商品信息失败: {str(e)}")
    
    def create_order_params(self, product_info, cookies, user_agent, product_url, session=None):
        """创建下单参数 - 完全按照原始版本的方式"""
        try:
            headers = {
                "Origin": "https://detail.tmall.com" if 'tmall.com' in product_url else "https://item.taobao.com",
                "Referer": product_url,
                "User-Agent": user_agent
            }
            
            # 如果没有传入session，创建新的session并设置cookies
            if session is None:
                session = requests.Session()
                
                # 设置cookies - 修复cookie设置方式
                if isinstance(cookies, str):
                    import json
                    try:
                        cookies = json.loads(cookies)
                    except:
                        cookie_dict = {}
                        for item in cookies.split(';'):
                            if '=' in item:
                                key, value = item.strip().split('=', 1)
                                cookie_dict[key] = value
                        cookies = cookie_dict
                
                # 根据URL确定域名，避免重复设置
                if 'tmall.com' in product_url:
                    domain = '.tmall.com'
                else:
                    domain = '.taobao.com'
                
                for name, value in cookies.items():
                    session.cookies.set(name, value, domain=domain)
            
            # 构建下单参数 - 完全按照原始版本
            order_data = {
                'title': '(unable to decode value)',
                'x_id': '',
                'seller_id': product_info.get('seller_id', ''),
                'seller_nickname': '(unable to decode value)',
                'who_pay_ship': '(unable to decode value)',
                'photo_url': product_info.get('photo_url', ''),
                'region': '(unable to decode value)',
                'auto_post': 'false',
                'etm': 'post',
                'virtual': 'false',
                'rootCatId': product_info.get('rootCatId', ''),
                'auto_post1': '',
                'buyer_from': 'ecity',
                'root_refer': '',
                'item_url_refer': 'https%3A%2F%2Fs.taobao.com%2F',
                'allow_quantity': product_info.get('allow_quantity', '1'),
                'buy_param': product_info.get('buy_param', ''),
                'quantity': '1',
                '_tb_token_': product_info.get('_tb_token_', ''),
                'skuInfo': '(unable to decode value)',
                'use_cod': 'false',
                '_input_charset': 'UTF-8',
                'destination': '350100',
                'skuId': product_info.get('sku_id', ''),
                'bankfrom': '',
                'from_etao': '',
                'item_id_num': product_info.get('item_id', ''),
                'item_id': product_info.get('item_id', ''),
                'auction_id': product_info.get('item_id', ''),
                'seller_rank': '0',
                'seller_rate_sum': '0',
                'is_orginal': 'no',
                'point_price': 'false',
                'secure_pay': 'true',
                'pay_method': '(unable to decode value)',
                'from': 'item_detail',
                'buy_now': product_info.get('buy_now', ''),
                'current_price': product_info.get('current_price', ''),
                'auction_type': 'b',
                'seller_num_id': product_info.get('seller_num_id', ''),
                'activity': '',
                'chargeTypeId': '',
            }
            
            return order_data, session, headers
            
        except Exception as e:
            raise Exception(f"创建下单参数失败: {str(e)}")
    
    def submit_order(self, order_data, session, headers, product_info, product_url):
        """提交订单 - 简化版本"""
        try:
            # 确定提交URL
            if 'tmall.com' in product_url:
                submit_url = "https://buy.tmall.com/order/confirm_order.htm"
            else:
                submit_url = "https://buy.taobao.com/auction/confirm_order.htm"
            
            start_time = time.time()
            
            # 直接提交订单
            response = session.post(
                submit_url,
                data=order_data,
                headers=headers,
                timeout=(3, 5),
                allow_redirects=True
            )
            
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            # 检查响应状态
            if response.status_code not in [200, 302]:
                return False, f"提交失败，状态码: {response.status_code}"
            
            response_text = response.text
            
            # 成功标识检查
            success_indicators = [
                "正在创建支付宝安全链接",
                "支付宝安全链接", 
                "订单提交成功",
                "payment.alipay.com",
                "cashier.alipay.com"
            ]
            
            for indicator in success_indicators:
                if indicator in response_text:
                    return True, f"秒杀成功！用时: {elapsed_time:.2f}秒"
            
            # 检查重定向到支付页面
            if response.status_code == 302:
                location = response.headers.get('Location', '')
                if 'alipay' in location.lower() or 'pay' in location.lower():
                    return True, f"秒杀成功！用时: {elapsed_time:.2f}秒"
            
            # 失败原因检查
            failure_indicators = [
                ("亲，小二正忙", "商品可能已售完"),
                ("验证码", "需要验证码验证"),
                ("登录", "登录状态失效"),
                ("库存不足", "商品库存不足"),
                ("商品已下架", "商品已下架"),
                ("系统繁忙", "系统繁忙，请稍后重试")
            ]
            
            for check_text, error_msg in failure_indicators:
                if check_text in response_text:
                    return False, error_msg
            
            return False, f"秒杀失败，用时: {elapsed_time:.2f}秒"
            
        except requests.exceptions.Timeout:
            return False, "请求超时"
        except requests.exceptions.ConnectionError:
            return False, "网络连接错误"
        except Exception as e:
            return False, f"提交订单异常: {str(e)}"
    
    def spike_worker(self, task_id, user_id, product_url, cookies, user_agent, callback=None):
        """单个用户的秒杀工作线程"""
        try:
            if callback:
                callback(f"用户 {user_id} 开始解析商品信息...")
            
            # 解析商品信息 - 修复返回值接收
            product_info, page_source, session = self.parse_product_info(product_url, cookies, user_agent)
            
            if callback:
                callback(f"用户 {user_id} 商品解析完成: {product_info['title']}")
            
            # 创建下单参数 - 复用session
            order_data, session, headers = self.create_order_params(
                product_info, cookies, user_agent, product_url, session
            )
            
            if callback:
                callback(f"用户 {user_id} 准备就绪，等待秒杀时间...")
            
            # 等待秒杀时间
            while True:
                with self.lock:
                    if task_id not in self.running_tasks:
                        return  # 任务已停止
                    
                    task_info = self.running_tasks[task_id]
                    if task_info['should_start']:
                        break
                
                time.sleep(0.1)
            
            if callback:
                callback(f"用户 {user_id} 开始秒杀...")
            
            # 执行秒杀
            success, message = self.submit_order(order_data, session, headers, product_info, product_url)
            
            # 更新结果
            result = 1 if success else 0
            self.db.update_task_user_result(task_id, user_id, result, message)
            
            if callback:
                status = "成功" if success else "失败"
                callback(f"用户 {user_id} 秒杀{status}: {message}")
            
            return success, message
            
        except Exception as e:
            error_msg = f"秒杀过程异常: {str(e)}"
            self.db.update_task_user_result(task_id, user_id, 0, error_msg)
            
            if callback:
                callback(f"用户 {user_id} 秒杀异常: {error_msg}")
            
            return False, error_msg
    
    def start_spike_task(self, task_id, spike_time, callback=None):
        """启动秒杀任务 - 优化版本，预处理+并发"""
        try:
            # 获取任务信息
            tasks = self.db.get_tasks()
            task = next((t for t in tasks if t['id'] == task_id), None)
            
            if not task:
                if callback:
                    callback("任务不存在")
                return False
            
            # 获取任务关联的用户
            task_users = self.db.get_task_users(task_id)
            
            if not task_users:
                if callback:
                    callback("任务没有关联的用户")
                return False
            
            # 获取用户详细信息
            users = self.db.get_users()
            user_dict = {u['id']: u for u in users}
            
            if callback:
                callback(f"任务 {task['task_name']} 准备启动，共 {len(task_users)} 个用户参与")
            
            # 初始化任务状态
            with self.lock:
                self.running_tasks[task_id] = {
                    'should_start': False,
                    'threads': [],
                    'task': task,
                    'users': task_users,
                    'prepared_data': {}  # 存储预处理的数据
                }
            
            # 预处理阶段：提前解析商品信息和准备下单参数
            if callback:
                callback("开始预处理商品信息...")
            
            product_url = task['product_url']
            
            # 为每个用户预处理数据
            for task_user in task_users:
                user_id = task_user['user_id']
                user = user_dict.get(user_id)
                
                if not user:
                    continue
                
                try:
                    # 预解析商品信息（使用第一个用户的cookie）
                    if 'product_info' not in self.running_tasks[task_id]['prepared_data']:
                        product_info, page_source, session = self.parse_product_info(
                            product_url, user['cookies'], user['user_agent']
                        )
                        self.running_tasks[task_id]['prepared_data']['product_info'] = product_info
                        self.running_tasks[task_id]['prepared_data']['page_source'] = page_source
                        
                        if callback:
                            callback(f"商品信息预处理完成: {product_info.get('title', '未知商品')}")
                    
                    # 为每个用户预创建下单参数
                    product_info = self.running_tasks[task_id]['prepared_data']['product_info']
                    order_data, session, headers = self.create_order_params(
                        product_info, user['cookies'], user['user_agent'], product_url
                    )
                    
                    # 存储预处理的数据
                    self.running_tasks[task_id]['prepared_data'][user_id] = {
                        'order_data': order_data,
                        'session': session,
                        'headers': headers,
                        'user': user
                    }
                    
                except Exception as e:
                    if callback:
                        callback(f"用户 {user_id} 预处理失败: {str(e)}")
                    continue
            
            if callback:
                callback(f"预处理完成，{len(self.running_tasks[task_id]['prepared_data']) - 1} 个用户准备就绪")
            
            # 启动优化的用户线程 - 并发执行
            threads = []
            
            for task_user in task_users:
                user_id = task_user['user_id']
                
                if user_id in self.running_tasks[task_id]['prepared_data']:
                    thread = threading.Thread(
                        target=self.optimized_spike_worker,
                        args=(task_id, user_id, callback)
                    )
                    thread.daemon = True
                    threads.append(thread)
                    
                    with self.lock:
                        self.running_tasks[task_id]['threads'].append(thread)
            
            # 启动所有线程
            for thread in threads:
                thread.start()
            
            if callback:
                callback(f"所有用户线程已启动，等待秒杀时间: {spike_time}")
            
            # 等待秒杀时间
            target_time = datetime.strptime(spike_time, "%Y-%m-%d %H:%M:%S")
            
            while True:
                current_time = datetime.now()
                time_diff = (target_time - current_time).total_seconds()
                
                if time_diff <= 0:
                    break
                elif time_diff <= 10:
                    if callback:
                        callback(f"距离秒杀开始还有 {time_diff:.1f} 秒")
                    time.sleep(0.1)
                else:
                    time.sleep(1)
            
            # 发出秒杀开始信号
            with self.lock:
                if task_id in self.running_tasks:
                    self.running_tasks[task_id]['should_start'] = True
            
            if callback:
                callback("秒杀开始！")
            
            # 更新任务状态
            self.db.update_task_status(task_id, 1)  # 1表示进行中
            
            # 等待所有线程完成
            for thread in threads:
                thread.join(timeout=30)  # 最多等待30秒
            
            # 清理任务状态
            with self.lock:
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]
            
            # 更新任务状态为完成
            self.db.update_task_status(task_id, 2)  # 2表示已完成
            
            if callback:
                callback("秒杀任务完成")
            
            return True
            
        except Exception as e:
            if callback:
                callback(f"启动秒杀任务失败: {str(e)}")
            
            # 清理任务状态
            with self.lock:
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]
            
            return False
    
    def optimized_spike_worker(self, task_id, user_id, callback=None):
        """优化的秒杀工作线程 - 使用预处理数据"""
        try:
            # 等待秒杀开始信号
            while True:
                with self.lock:
                    if task_id not in self.running_tasks:
                        return  # 任务已停止
                    
                    task_info = self.running_tasks[task_id]
                    if task_info['should_start']:
                        break
                
                time.sleep(0.01)  # 更短的等待间隔，提高响应速度
            
            # 获取预处理的数据
            with self.lock:
                if task_id not in self.running_tasks:
                    return
                
                prepared_data = self.running_tasks[task_id]['prepared_data'].get(user_id)
                if not prepared_data:
                    if callback:
                        callback(f"用户 {user_id} 预处理数据不存在")
                    return
                
                product_info = self.running_tasks[task_id]['prepared_data']['product_info']
                product_url = self.running_tasks[task_id]['task']['product_url']
            
            if callback:
                callback(f"用户 {user_id} 开始秒杀...")
            
            start_time = time.time()
            
            # 直接使用预处理的数据执行秒杀
            success, message = self.submit_order(
                prepared_data['order_data'],
                prepared_data['session'],
                prepared_data['headers'],
                product_info,
                product_url
            )
            
            end_time = time.time()
            elapsed_time = end_time - start_time
            
            # 更新结果
            result = 1 if success else 0
            self.db.update_task_user_result(task_id, user_id, result, message)
            
            if callback:
                status = "成功" if success else "失败"
                callback(f"用户 {user_id} 秒杀{status}: {message}，用时: {elapsed_time:.2f}秒")
            
            return success, message
            
        except Exception as e:
            error_msg = f"秒杀过程异常: {str(e)}"
            self.db.update_task_user_result(task_id, user_id, 0, error_msg)
            
            if callback:
                callback(f"用户 {user_id} 秒杀异常: {error_msg}")
            
            return False, error_msg
    
    def stop_spike_task(self, task_id):
        """停止秒杀任务"""
        with self.lock:
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
                return True
        return False
    
    def get_running_tasks(self):
        """获取正在运行的任务"""
        with self.lock:
            return list(self.running_tasks.keys())