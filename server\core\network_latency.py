#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网络延迟检测和补偿模块
功能：检测网络延迟，计算执行时间补偿，提高秒杀精确度
"""

import time
import requests
import statistics
import threading
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import json

logger = logging.getLogger(__name__)


class NetworkLatencyDetector:
    """网络延迟检测器"""
    
    def __init__(self):
        self.latency_cache = {}  # 缓存延迟数据
        self.cache_duration = 300  # 缓存5分钟
        self.test_urls = [
            'https://www.taobao.com/favicon.ico',
            'https://www.tmall.com/favicon.ico',
            'https://g.alicdn.com/mtb/lib-mtop/2.6.1/mtop.js',
        ]
        self.lock = threading.Lock()
        
    def detect_latency(self, target_domain: str = None, samples: int = 5) -> Dict:
        """
        检测网络延迟
        
        Args:
            target_domain: 目标域名，如果不指定则使用默认测试URL
            samples: 采样次数
            
        Returns:
            延迟统计信息
        """
        cache_key = target_domain or 'default'
        
        # 检查缓存
        with self.lock:
            if cache_key in self.latency_cache:
                cached_data = self.latency_cache[cache_key]
                if time.time() - cached_data['timestamp'] < self.cache_duration:
                    logger.debug(f"使用缓存的延迟数据: {cached_data['stats']}")
                    return cached_data['stats']
        
        # 执行延迟检测
        latencies = []
        test_urls = [f"https://{target_domain}/favicon.ico"] if target_domain else self.test_urls
        
        logger.info(f"开始检测网络延迟，采样次数: {samples}")
        
        for i in range(samples):
            for url in test_urls:
                try:
                    start_time = time.time() * 1000  # 毫秒
                    
                    response = requests.head(url, timeout=5, allow_redirects=True)
                    
                    end_time = time.time() * 1000  # 毫秒
                    latency = end_time - start_time
                    
                    if response.status_code < 400:  # 成功响应
                        latencies.append(latency)
                        logger.debug(f"延迟检测 {i+1}/{samples}: {latency:.2f}ms ({url})")
                    
                except Exception as e:
                    logger.warning(f"延迟检测失败: {url} - {e}")
                    continue
            
            # 采样间隔
            if i < samples - 1:
                time.sleep(0.1)
        
        if not latencies:
            logger.error("所有延迟检测都失败了")
            return {
                'avg_latency': 0,
                'min_latency': 0,
                'max_latency': 0,
                'median_latency': 0,
                'std_deviation': 0,
                'sample_count': 0,
                'reliability': 0
            }
        
        # 计算统计信息
        stats = {
            'avg_latency': statistics.mean(latencies),
            'min_latency': min(latencies),
            'max_latency': max(latencies),
            'median_latency': statistics.median(latencies),
            'std_deviation': statistics.stdev(latencies) if len(latencies) > 1 else 0,
            'sample_count': len(latencies),
            'reliability': len(latencies) / (samples * len(test_urls))  # 成功率
        }
        
        # 缓存结果
        with self.lock:
            self.latency_cache[cache_key] = {
                'stats': stats,
                'timestamp': time.time()
            }
        
        logger.info(f"延迟检测完成: 平均{stats['avg_latency']:.2f}ms, "
                   f"中位数{stats['median_latency']:.2f}ms, "
                   f"标准差{stats['std_deviation']:.2f}ms")
        
        return stats
    
    def calculate_execution_offset(self, target_domain: str = None) -> Tuple[float, Dict]:
        """
        计算执行时间偏移量
        
        Args:
            target_domain: 目标域名
            
        Returns:
            (偏移毫秒数, 延迟统计信息)
        """
        latency_stats = self.detect_latency(target_domain)
        
        if latency_stats['sample_count'] == 0:
            return 0, latency_stats
        
        # 使用中位数 + 1个标准差作为保守估计
        # 这样可以覆盖大部分情况，避免过于激进
        offset_ms = latency_stats['median_latency'] + latency_stats['std_deviation']
        
        # 限制偏移量范围（避免过度补偿）
        offset_ms = max(10, min(offset_ms, 2000))  # 10ms到2000ms之间
        
        logger.info(f"计算执行偏移量: {offset_ms:.2f}ms")
        
        return offset_ms, latency_stats


class SeckillTimingOptimizer:
    """秒杀时机优化器"""
    
    def __init__(self):
        self.latency_detector = NetworkLatencyDetector()
        self.execution_history = []  # 执行历史记录
        self.max_history = 100
        
    def optimize_execution_time(self, scheduled_time: datetime, 
                              target_domain: str = None) -> Tuple[datetime, Dict]:
        """
        优化执行时间
        
        Args:
            scheduled_time: 原定执行时间
            target_domain: 目标域名
            
        Returns:
            (优化后的执行时间, 优化信息)
        """
        # 检测网络延迟
        offset_ms, latency_stats = self.latency_detector.calculate_execution_offset(target_domain)
        
        # 计算优化后的执行时间
        optimized_time = scheduled_time - timedelta(milliseconds=offset_ms)
        
        # 确保不会提前太多（最多提前5秒）
        max_advance = timedelta(seconds=5)
        current_time = datetime.now()
        
        if scheduled_time - optimized_time > max_advance:
            optimized_time = scheduled_time - max_advance
            offset_ms = max_advance.total_seconds() * 1000
        
        # 确保不会在过去执行
        if optimized_time <= current_time:
            optimized_time = current_time + timedelta(milliseconds=100)
            offset_ms = (scheduled_time - optimized_time).total_seconds() * 1000
        
        optimization_info = {
            'original_time': scheduled_time,
            'optimized_time': optimized_time,
            'offset_ms': offset_ms,
            'latency_stats': latency_stats,
            'advance_seconds': (scheduled_time - optimized_time).total_seconds()
        }
        
        logger.info(f"时机优化完成: 提前{optimization_info['advance_seconds']:.3f}秒执行 "
                   f"(偏移量: {offset_ms:.2f}ms)")
        
        return optimized_time, optimization_info
    
    def record_execution_result(self, optimization_info: Dict, 
                              actual_execution_time: datetime,
                              success: bool, execution_duration_ms: float):
        """
        记录执行结果，用于后续优化
        
        Args:
            optimization_info: 优化信息
            actual_execution_time: 实际执行时间
            success: 是否成功
            execution_duration_ms: 执行耗时（毫秒）
        """
        record = {
            'timestamp': datetime.now(),
            'optimization_info': optimization_info,
            'actual_execution_time': actual_execution_time,
            'success': success,
            'execution_duration_ms': execution_duration_ms,
            'timing_accuracy': abs((actual_execution_time - optimization_info['original_time']).total_seconds())
        }
        
        self.execution_history.append(record)
        
        # 限制历史记录数量
        if len(self.execution_history) > self.max_history:
            self.execution_history.pop(0)
        
        logger.info(f"记录执行结果: 成功={success}, 耗时={execution_duration_ms:.2f}ms, "
                   f"时机精度={record['timing_accuracy']:.3f}s")
    
    def get_performance_stats(self) -> Dict:
        """获取性能统计信息"""
        if not self.execution_history:
            return {
                'total_tasks': 0,
                'success_rate': 0,
                'avg_execution_time': 0,
                'avg_optimization_time': 0,
                'total_executions': 0,
                'recent_success_rate': 0,
                'avg_execution_duration_ms': 0,
                'avg_timing_accuracy_seconds': 0,
                'last_execution': None
            }
        
        recent_records = self.execution_history[-20:]  # 最近20次
        all_records = self.execution_history
        
        # 计算总体统计
        total_success_rate = sum(1 for r in all_records if r['success']) / len(all_records)
        recent_success_rate = sum(1 for r in recent_records if r['success']) / len(recent_records)
        avg_duration = statistics.mean([r['execution_duration_ms'] for r in recent_records])
        avg_timing_accuracy = statistics.mean([r['timing_accuracy'] for r in recent_records])
        avg_optimization_time = statistics.mean([r['optimization_info']['offset_ms'] for r in recent_records])
        
        return {
            'total_tasks': len(all_records),
            'success_rate': total_success_rate,
            'avg_execution_time': avg_duration,
            'avg_optimization_time': avg_optimization_time,
            'total_executions': len(all_records),
            'recent_success_rate': recent_success_rate,
            'avg_execution_duration_ms': avg_duration,
            'avg_timing_accuracy_seconds': avg_timing_accuracy,
            'last_execution': all_records[-1]['timestamp'].isoformat() if all_records else None
        }


# 全局实例
timing_optimizer = SeckillTimingOptimizer()


def get_optimized_execution_time(scheduled_time: datetime, 
                               target_domain: str = None) -> Tuple[datetime, Dict]:
    """
    获取优化后的执行时间（便捷函数）
    
    Args:
        scheduled_time: 原定执行时间
        target_domain: 目标域名
        
    Returns:
        (优化后的执行时间, 优化信息)
    """
    return timing_optimizer.optimize_execution_time(scheduled_time, target_domain)


def record_seckill_result(optimization_info: Dict, 
                         actual_execution_time: datetime,
                         success: bool, execution_duration_ms: float):
    """
    记录秒杀执行结果（便捷函数）
    """
    timing_optimizer.record_execution_result(
        optimization_info, actual_execution_time, success, execution_duration_ms
    )


def get_timing_performance_stats() -> Dict:
    """获取时机优化性能统计（便捷函数）"""
    return timing_optimizer.get_performance_stats()