#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
秒杀核心引擎
功能：基于原始版本思路，实现多线程、多任务的秒杀核心功能
"""

import threading
import time
import queue
import requests
import json
import re
from datetime import datetime
from urllib.parse import parse_qs, urlparse, quote
import logging
from typing import Dict, List, Optional
import parsel
from .timer import get_accurate_time, wait_until_time, get_time_status
from .network_latency import get_optimized_execution_time, record_seckill_result, get_timing_performance_stats

logger = logging.getLogger(__name__)


class SeckillEngine:
    """秒杀引擎"""
    
    def __init__(self, db, cookie_model, task_model, log_model):
        self.db = db
        self.cookie_model = cookie_model
        self.task_model = task_model
        self.log_model = log_model
        
        self.is_running = False
        self.task_queue = queue.Queue()
        self.worker_threads = []
        self.max_workers = 10
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'success_tasks': 0,
            'failed_tasks': 0,
            'running_tasks': 0
        }
        
    def start(self):
        """启动秒杀引擎"""
        if self.is_running:
            return
            
        self.is_running = True
        logger.info("秒杀引擎启动")
        
        # 启动任务调度线程
        scheduler_thread = threading.Thread(target=self._task_scheduler, daemon=True)
        scheduler_thread.start()
        
        # 启动工作线程
        for i in range(self.max_workers):
            worker_thread = threading.Thread(target=self._worker, daemon=True)
            worker_thread.start()
            self.worker_threads.append(worker_thread)
            
        logger.info(f"秒杀引擎已启动，工作线程数: {self.max_workers}")
        
    def stop(self):
        """停止秒杀引擎"""
        self.is_running = False
        logger.info("秒杀引擎已停止")
        
    def _task_scheduler(self):
        """任务调度器（使用准确的服务器时间和网络延迟补偿）"""
        while self.is_running:
            try:
                # 获取待执行任务
                pending_tasks = self.task_model.get_pending_tasks(limit=50)
                
                # 获取准确的服务器时间
                current_server_time = datetime.fromtimestamp(get_accurate_time() / 1000)
                
                for task in pending_tasks:
                    # 获取目标域名（用于延迟检测）
                    target_domain = self._extract_domain(task['product_url'])
                    
                    # 使用网络延迟补偿优化执行时间
                    optimized_time, optimization_info = get_optimized_execution_time(
                        task['scheduled_time'], target_domain
                    )
                    
                    # 检查是否到了优化后的执行时间
                    if optimized_time <= current_server_time:
                        # 将任务和优化信息一起加入队列
                        task_with_optimization = task.copy()
                        task_with_optimization['optimization_info'] = optimization_info
                        task_with_optimization['optimized_execution_time'] = optimized_time
                        
                        self.task_queue.put(task_with_optimization)
                        
                        # 更新任务状态为运行中
                        self.task_model.update_task_status(task['task_id'], 'running')
                        
                        # 记录时间信息
                        time_info = get_time_status()
                        advance_ms = optimization_info['offset_ms']
                        self.log_model.add_log('INFO', 
                                             f'任务加入执行队列: {task["product_url"]} '
                                             f'(服务器时间: {time_info["server_time_str"]}, '
                                             f'时间差: {time_info["time_diff"]}ms, '
                                             f'网络延迟补偿: {advance_ms:.2f}ms)',
                                             task_id=task['task_id'], user_id=task['user_id'])
                        
                # 每0.5秒检查一次（提高精度，支持毫秒级调度）
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"任务调度器出错: {e}")
                time.sleep(5)
    
    def _extract_domain(self, url: str) -> str:
        """从URL中提取域名"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc
        except:
            return 'www.tmall.com'  # 默认域名
                
    def _worker(self):
        """工作线程"""
        while self.is_running:
            try:
                # 从队列获取任务
                task = self.task_queue.get(timeout=1)
                
                self.stats['running_tasks'] += 1
                self.stats['total_tasks'] += 1
                
                # 执行秒杀任务
                success = self._execute_seckill_task(task)
                
                self.stats['running_tasks'] -= 1
                if success:
                    self.stats['success_tasks'] += 1
                else:
                    self.stats['failed_tasks'] += 1
                    
                self.task_queue.task_done()
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"工作线程出错: {e}")
                self.stats['running_tasks'] -= 1
                self.stats['failed_tasks'] += 1
                
    def _execute_seckill_task(self, task):
        """执行秒杀任务"""
        task_id = task['task_id']
        user_id = task['user_id']
        product_url = task['product_url']
        
        # 记录执行开始时间
        execution_start_time = datetime.now()
        start_timestamp = time.time() * 1000  # 毫秒
        
        # 获取优化信息（如果有）
        optimization_info = task.get('optimization_info')
        optimized_execution_time = task.get('optimized_execution_time')
        
        try:
            # 获取用户Cookie
            cookie_data = self.cookie_model.get_valid_cookies(user_id)
            if not cookie_data:
                self.log_model.add_log('ERROR', '用户Cookie无效或已过期',
                                     task_id=task_id, user_id=user_id)
                self.task_model.update_task_status(task_id, 'failed', 
                                                 error_message='Cookie无效或已过期')
                return False
                
            cookies = cookie_data['cookies']
            
            # 创建会话
            session = requests.Session()
            
            # 设置Cookie
            for name, value in cookies.items():
                session.cookies.set(name, value)
                
            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
            
            # 执行秒杀流程
            success = self._perform_seckill(session, headers, product_url, task_id, user_id)
            
            # 计算执行耗时
            end_timestamp = time.time() * 1000  # 毫秒
            execution_duration_ms = end_timestamp - start_timestamp
            
            # 记录执行结果到优化器
            if optimization_info and optimized_execution_time:
                record_seckill_result(
                    optimization_info, 
                    execution_start_time,
                    success, 
                    execution_duration_ms
                )
            
            if success:
                self.task_model.update_task_status(task_id, 'success', 
                                                 result={'message': '秒杀成功', 'execution_duration_ms': execution_duration_ms})
                self.log_model.add_log('INFO', f'秒杀任务执行成功，耗时: {execution_duration_ms:.2f}ms',
                                     task_id=task_id, user_id=user_id)
            else:
                self.task_model.update_task_status(task_id, 'failed',
                                                 error_message=f'秒杀执行失败，耗时: {execution_duration_ms:.2f}ms')
                self.log_model.add_log('ERROR', f'秒杀任务执行失败，耗时: {execution_duration_ms:.2f}ms',
                                     task_id=task_id, user_id=user_id)
                
            return success
            
        except Exception as e:
            # 计算执行耗时（即使出错也要记录）
            end_timestamp = time.time() * 1000  # 毫秒
            execution_duration_ms = end_timestamp - start_timestamp
            
            # 记录执行结果到优化器
            if optimization_info and optimized_execution_time:
                record_seckill_result(
                    optimization_info, 
                    execution_start_time,
                    False, 
                    execution_duration_ms
                )
            
            logger.error(f"执行秒杀任务出错: {e}")
            self.task_model.update_task_status(task_id, 'failed', error_message=f'{str(e)}，耗时: {execution_duration_ms:.2f}ms')
            self.log_model.add_log('ERROR', f'秒杀任务执行异常: {str(e)}，耗时: {execution_duration_ms:.2f}ms',
                                 task_id=task_id, user_id=user_id)
            return False
            
    def _perform_seckill(self, session, headers, product_url, task_id, user_id):
        """执行秒杀流程（基于原始版本逻辑）"""
        try:
            # 1. 获取商品页面
            self.log_model.add_log('INFO', f'正在访问商品页面: {product_url}',
                                 task_id=task_id, user_id=user_id)
            
            response = session.get(product_url, headers=headers, timeout=10)
            if response.status_code != 200:
                self.log_model.add_log('ERROR', f'访问商品页面失败: {response.status_code}',
                                     task_id=task_id, user_id=user_id)
                return False
                
            page_content = response.text
            
            # 2. 解析商品信息和构建详细参数（基于原始版本逻辑）
            detail_params = self._get_detail_params(product_url, page_content, session.cookies)
            if not detail_params:
                self.log_model.add_log('ERROR', '解析商品详细参数失败',
                                     task_id=task_id, user_id=user_id)
                return False
                
            g_data, item_id = detail_params
            
            # 3. 第一步：确认订单（获取提交参数）
            self.log_model.add_log('INFO', '正在确认订单...',
                                 task_id=task_id, user_id=user_id)
            
            confirm_result = self._confirm_order_step1(session, headers, g_data, product_url, session.cookies)
            if not confirm_result:
                self.log_model.add_log('ERROR', '订单确认失败',
                                     task_id=task_id, user_id=user_id)
                return False
                
            c_data, secret_value, sparam1, confirm_url = confirm_result
            
            # 4. 第二步：提交最终订单
            self.log_model.add_log('INFO', '正在提交最终订单...',
                                 task_id=task_id, user_id=user_id)
            
            submit_result = self._submit_order_step2(session, headers, c_data, item_id, 
                                                   secret_value, sparam1, confirm_url, session.cookies)
            if submit_result:
                self.log_model.add_log('INFO', '订单提交成功！',
                                     task_id=task_id, user_id=user_id)
                return True
            else:
                self.log_model.add_log('ERROR', '订单提交失败',
                                     task_id=task_id, user_id=user_id)
                return False
                
        except Exception as e:
            logger.error(f"秒杀流程执行出错: {e}")
            self.log_model.add_log('ERROR', f'秒杀流程异常: {str(e)}',
                                 task_id=task_id, user_id=user_id)
            return False
            
    def _get_detail_params(self, url, page_source, cookies):
        """获取详细参数（基于原始版本逻辑）"""
        try:
            import parsel
            from urllib.parse import quote
            
            selector = parsel.Selector(page_source)
            
            # 提取基本参数
            seller_id = selector.xpath("//form[@id='J_FrmBid']/input[@name='seller_id']/@value").get()
            photo_url = selector.xpath("//form[@id='J_FrmBid']/input[@name='photo_url']/@value").get()
            rootCatId = selector.xpath("//form[@id='J_FrmBid']/input[@name='rootCatId']/@value").get()
            seller_num_id = selector.xpath("//*[@id=\"dsr-userid\"]/@value").get()
            
            # 从页面源码中提取数量和价格信息
            allow_quantity_match = re.search(r'"quantity":(\d+),', page_source)
            allow_quantity = allow_quantity_match.group(1) if allow_quantity_match else "1"
            
            # 解析URL中的商品ID和SKU ID
            param_match = re.search(r'id=(\d+).*&skuId=(\d+)', url)
            if not param_match:
                logger.error("无法从URL中提取商品ID和SKU ID")
                return None
                
            item_id_num = param_match.group(1)
            sku_id = param_match.group(2)
            buy_param = f"{item_id_num}_1_{sku_id}"
            
            # 提取价格信息
            price_match = re.search(r'"price":"(\d+\.\d+)",', page_source)
            buy_now = price_match.group(1) if price_match else "0.00"
            current_price = buy_now
            
            # 获取token
            _tb_token_ = cookies.get("_tb_token_", "")
            
            # 构建详细参数数据
            data = {
                'title': '(unable to decode value)',
                'x_id': '',
                'seller_id': seller_id or '',
                'seller_nickname': '(unable to decode value)',
                'who_pay_ship': '(unable to decode value)',
                'photo_url': photo_url or '',
                'region': '(unable to decode value)',
                'auto_post': 'false',
                'etm': 'post',
                'virtual': 'false',
                'rootCatId': rootCatId or '',
                'auto_post1': '',
                'buyer_from': 'ecity',
                'root_refer': '',
                'item_url_refer': 'https%3A%2F%2Fs.taobao.com%2F',
                'allow_quantity': allow_quantity,
                'buy_param': buy_param,
                'quantity': '1',
                '_tb_token_': _tb_token_,
                'skuInfo': '(unable to decode value)',
                'use_cod': 'false',
                '_input_charset': 'UTF-8',
                'destination': '350100',
                'skuId': sku_id,
                'bankfrom': '',
                'from_etao': '',
                'item_id_num': item_id_num,
                'item_id': item_id_num,
                'auction_id': item_id_num,
                'seller_rank': '0',
                'seller_rate_sum': '0',
                'is_orginal': 'no',
                'point_price': 'false',
                'secure_pay': 'true',
                'pay_method': '(unable to decode value)',
                'from': 'item_detail',
                'buy_now': buy_now,
                'current_price': current_price,
                'auction_type': 'b',
                'seller_num_id': seller_num_id or '',
                'activity': '',
                'chargeTypeId': '',
            }
            
            return data, item_id_num
            
        except Exception as e:
            logger.error(f"获取详细参数出错: {e}")
            return None
            
    def _confirm_order_step1(self, session, headers, g_data, ref_url, cookies):
        """第一步：确认订单（基于原始版本逻辑）"""
        try:
            from urllib.parse import quote, unquote
            
            url = "https://buy.tmall.com/order/confirm_order.htm"
            
            headers_copy = headers.copy()
            headers_copy.update({
                "Origin": "https://detail.tmall.com",
                "Referer": ref_url,
                "Content-Type": "application/x-www-form-urlencoded"
            })
            
            params = {
                "x-itemid": g_data.get("item_id_num"),
                "x-uid": cookies.get("unb", "")
            }
            
            response = session.post(url, headers=headers_copy, data=g_data, params=params, timeout=15)
            
            if response.status_code != 200:
                logger.error(f"确认订单请求失败，状态码: {response.status_code}")
                return None
                
            # 增加调试信息
            logger.info(f"确认订单响应状态码: {response.status_code}")
            logger.info(f"响应内容长度: {len(response.text)}")
            
            # 检查响应中是否包含orderData
            if "var orderData=" not in response.text:
                logger.warning("响应中未找到 'var orderData=' 声明")
                # 检查是否是商品售罄
                sold_out_patterns = [
                    "亲，小二正忙",
                    "商品已售罄", 
                    "库存不足",
                    "商品已下架"
                ]
                for pattern in sold_out_patterns:
                    if pattern in response.text:
                        logger.warning(f"检测到售罄信息: {pattern}")
                        return None
                
                # 保存响应片段用于调试
                snippet = response.text[:2000] if len(response.text) > 2000 else response.text
                logger.debug(f"响应内容片段: {snippet}")
                return None
            
            # 使用原始版本的解析方法，但增加更多容错性
            res_data = None
            
            try:
                # 方案1：原始版本的精确匹配
                logger.info("尝试原始版本的精确匹配方法")
                res_match = re.search(r"var orderData= \{.*\{\"data\":\[\]\},\"reload\":true\}", response.text)
                if res_match:
                    logger.info("精确匹配成功")
                    res = res_match.group()
                    json_match = re.findall("var orderData= (.*)", res)
                    if json_match:
                        res_data = json.loads(json.loads(json.dumps(json_match), encoding="utf-8")[0])
                        logger.info("原始版本解析成功")
                    else:
                        logger.warning("精确匹配后无法提取JSON")
                else:
                    logger.info("精确匹配失败，尝试其他方案")
                    
            except json.JSONDecodeError as e:
                logger.error(f"原始版本JSON解析失败: {e}")
            except Exception as e:
                logger.error(f"原始版本解析出错: {e}")
            
            # 方案2：更宽松的匹配
            if res_data is None:
                try:
                    logger.info("尝试宽松匹配方法")
                    # 查找任何形式的orderData
                    patterns = [
                        r"var orderData= (\{.*?\});",
                        r"var orderData=(\{.*?\});",
                        r"orderData = (\{.*?\});",
                        r"orderData=(\{.*?\});"
                    ]
                    
                    for i, pattern in enumerate(patterns):
                        logger.info(f"尝试模式 {i+1}: {pattern}")
                        matches = re.findall(pattern, response.text, re.DOTALL)
                        if matches:
                            logger.info(f"模式 {i+1} 找到 {len(matches)} 个匹配")
                            for j, match in enumerate(matches):
                                try:
                                    # 尝试找到完整的JSON结构
                                    brace_count = 0
                                    valid_end = 0
                                    for k, char in enumerate(match):
                                        if char == '{':
                                            brace_count += 1
                                        elif char == '}':
                                            brace_count -= 1
                                            if brace_count == 0:
                                                valid_end = k + 1
                                                break
                                    
                                    if valid_end > 0:
                                        truncated_json = match[:valid_end]
                                        res_data = json.loads(truncated_json)
                                        logger.info(f"模式 {i+1} 第 {j+1} 个匹配解析成功")
                                        break
                                    else:
                                        logger.warning(f"模式 {i+1} 第 {j+1} 个匹配无法找到完整JSON结构")
                                except json.JSONDecodeError as e:
                                    logger.warning(f"模式 {i+1} 第 {j+1} 个匹配JSON解析失败: {e}")
                                    continue
                                except Exception as e:
                                    logger.warning(f"模式 {i+1} 第 {j+1} 个匹配处理出错: {e}")
                                    continue
                            if res_data:
                                break
                        else:
                            logger.info(f"模式 {i+1} 未找到匹配")
                            
                except Exception as e:
                    logger.error(f"宽松匹配出错: {e}")
            
            # 方案3：最后的尝试 - 查找所有可能的JSON对象
            if res_data is None:
                try:
                    logger.info("尝试最后的JSON对象搜索")
                    # 查找所有可能包含data字段的JSON对象
                    json_objects = re.findall(r'\{[^{}]*"data"[^{}]*\}', response.text)
                    logger.info(f"找到 {len(json_objects)} 个可能的JSON对象")
                    
                    for i, obj in enumerate(json_objects):
                        try:
                            parsed = json.loads(obj)
                            if isinstance(parsed.get('data'), dict):
                                res_data = parsed
                                logger.info(f"第 {i+1} 个JSON对象解析成功")
                                break
                        except:
                            continue
                            
                except Exception as e:
                    logger.error(f"JSON对象搜索出错: {e}")
            
            if res_data is None:
                logger.error("所有解析方案都失败了")
                # 保存更多调试信息
                orderdata_positions = []
                for match in re.finditer(r"orderData", response.text):
                    start = max(0, match.start() - 100)
                    end = min(len(response.text), match.end() + 100)
                    orderdata_positions.append(response.text[start:end])
                
                if orderdata_positions:
                    logger.debug(f"找到 {len(orderdata_positions)} 个orderData位置:")
                    for i, pos in enumerate(orderdata_positions[:3]):  # 只记录前3个
                        logger.debug(f"位置 {i+1}: {pos}")
                
                return None

            # 提取关键参数
            try:
                logger.info("开始提取订单参数")
                logger.info(f"res_data结构: {list(res_data.keys()) if res_data else 'None'}")
                
                if 'data' not in res_data:
                    logger.error("res_data中没有data字段")
                    return None
                    
                data_keys = list(res_data['data'].keys())
                logger.info(f"data字段包含的键: {data_keys}")
                
                # 查找提交相关的键
                submit_key = None
                for key in data_keys:
                    if 'submit' in key.lower():
                        submit_key = key
                        break
                
                if not submit_key:
                    logger.error("未找到提交相关的键")
                    return None
                    
                logger.info(f"使用提交键: {submit_key}")
                submit_data = res_data['data'][submit_key]
                
                if 'hidden' not in submit_data or 'extensionMap' not in submit_data['hidden']:
                    logger.error("未找到hidden/extensionMap结构")
                    return None
                    
                hidden_data = submit_data['hidden']['extensionMap']
                logger.info(f"extensionMap包含的键: {list(hidden_data.keys())}")
                
                secret_value = hidden_data.get("secretValue")
                sparam1 = hidden_data.get("sparam1")
                action = hidden_data.get("action")
                event_submit_do_confirm = hidden_data.get("event_submit_do_confirm")
                input_charset = hidden_data.get("input_charset")
                unit_suffix = hidden_data.get("unitSuffix", "")
                
                logger.info(f"关键参数提取结果: secretValue={bool(secret_value)}, sparam1={bool(sparam1)}, action={bool(action)}, event_submit_do_confirm={bool(event_submit_do_confirm)}")
                
                if not all([secret_value, sparam1, action, event_submit_do_confirm]):
                    logger.error("关键参数缺失")
                    missing = []
                    if not secret_value: missing.append("secretValue")
                    if not sparam1: missing.append("sparam1") 
                    if not action: missing.append("action")
                    if not event_submit_do_confirm: missing.append("event_submit_do_confirm")
                    logger.error(f"缺失的参数: {missing}")
                    return None
                    
                # 构建提交数据（基于原始版本逻辑）
                endpoint = quote(json.dumps(res_data.get("endpoint", {})).replace(": ", ":").replace(", ", ","))
                
                linkage = {
                    "common": res_data.get("linkage", {}).get("common", {}),
                    "signature": res_data.get("linkage", {}).get("signature", {})
                }
                linkage_str = quote(json.dumps(linkage).replace(": ", ":").replace(", ", ","))
                
                # 构建提交数据
                p_data = {}
                for k, v in res_data.get("data", {}).items():
                    if res_data.get("data", {}).get(k, {}).get("submit"):
                        p_data[k] = res_data.get("data", {}).get(k)
                p_data_str = quote(json.dumps(p_data).replace(": ", ":").replace(", ", ","))
                
                hierarchy_str = quote(json.dumps(res_data.get("hierarchy", {}).get("structure", {})).replace(": ", ":").replace(", ", ","))
                
                praper_alipay_cashier_domain = "cashierrz" + unit_suffix[2:] if len(unit_suffix) > 2 else "cashierrz"
                
                c_data = {
                    "endpoint": endpoint,
                    "linkage": linkage_str,
                    "data": p_data_str,
                    "action": action,
                    "_tb_token_": g_data.get("_tb_token_", ""),
                    "event_submit_do_confirm": event_submit_do_confirm,
                    "praper_alipay_cashier_domain": praper_alipay_cashier_domain,
                    "input_charset": input_charset,
                    "hierarchy": hierarchy_str,
                }
                
                logger.info("订单参数构建成功")
                return c_data, secret_value, sparam1, response.url
                
            except Exception as e:
                logger.error(f"提取订单参数出错: {e}")
                import traceback
                logger.error(f"详细错误信息: {traceback.format_exc()}")
                return None
                
        except Exception as e:
            logger.error(f"确认订单出错: {e}")
            import traceback
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            return None
            
    def _submit_order_step2(self, session, headers, c_data, item_id, secret_value, sparam1, confirm_url, cookies):
        """第二步：提交最终订单（基于原始版本逻辑）"""
        try:
            url = "https://buy.tmall.com/auction/confirm_order.htm"
            
            headers_copy = headers.copy()
            headers_copy.update({
                'Referer': confirm_url,
                'Origin': "https://buy.tmall.com",
                'Content-Type': 'application/x-www-form-urlencoded'
            })
            
            params = {
                'x-itemid': item_id,
                'x-uid': cookies.get("unb", ""),
                'submitref': secret_value,
                'sparam1': sparam1
            }
            
            response = session.post(url, headers=headers_copy, params=params, data=c_data, timeout=15)
            
            if response.status_code == 200:
                # 检查成功标识（基于原始版本逻辑）
                success_pattern = r'<p class="youxianchupin">正在创建支付宝安全链接...</p>'
                success_match = re.search(success_pattern, response.text)
                
                if success_match:
                    logger.info("订单提交成功，正在创建支付宝安全链接")
                    return True
                else:
                    # 检查其他成功标识
                    other_success_indicators = [
                        '订单提交成功',
                        'alipay.com',
                        '正在创建支付宝安全链接'
                    ]
                    
                    for indicator in other_success_indicators:
                        if indicator in response.text:
                            logger.info(f"订单提交成功，检测到标识: {indicator}")
                            return True
                    
                    # 检查失败原因
                    failure_indicators = [
                        '亲，小二正忙',
                        '库存不足',
                        '商品已下架',
                        '系统繁忙'
                    ]
                    
                    for indicator in failure_indicators:
                        if indicator in response.text:
                            logger.warning(f"订单提交失败: {indicator}")
                            return False
                    
                    logger.warning("订单提交结果未知，未检测到明确的成功或失败标识")
                    return False
            else:
                logger.error(f"订单提交请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"提交最终订单出错: {e}")
            return False
            
    def get_status(self):
        """获取引擎状态"""
        return {
            'is_running': self.is_running,
            'queue_size': self.task_queue.qsize(),
            'worker_count': len(self.worker_threads),
            'statistics': self.stats.copy()
        }
        
    def get_stats(self):
        """获取引擎统计信息"""
        timing_stats = get_timing_performance_stats()
        
        return {
            'engine_stats': self.stats.copy(),
            'timing_optimization_stats': timing_stats
        }
